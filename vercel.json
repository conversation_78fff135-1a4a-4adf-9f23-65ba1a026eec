{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install --legacy-peer-deps", "devCommand": "npm run dev", "rewrites": [{"source": "/admin", "destination": "/admin/index.html"}, {"source": "/admin/(.*)", "destination": "/admin/index.html"}], "headers": [{"source": "/admin/(.*)", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}]}]}