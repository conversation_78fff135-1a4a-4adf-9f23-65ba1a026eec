---
title: "About"
slug: "about"
draft: false
---

# About This Blog

Welcome to my personal blog powered by DecapCMS and deployed on Vercel!

## What is this blog about?

This blog serves as a demonstration of how to set up a modern, git-based content management system using:

- **DecapCMS**: An open-source, git-based CMS
- **Next.js**: A React framework for production
- **Vercel**: A platform for frontend frameworks and static sites
- **GitHub**: For version control and content storage

## How it works

1. **Content Creation**: Write posts using the DecapCMS admin interface
2. **Git Integration**: Changes are automatically committed to the repository
3. **Review Process**: Use pull requests to review content before publishing
4. **Automatic Deployment**: Vercel automatically deploys changes when merged

## Features

- ✅ Git-based workflow
- ✅ Editorial review process
- ✅ Markdown support
- ✅ Image uploads
- ✅ SEO-friendly
- ✅ Mobile responsive
- ✅ Fast and secure

## Get Started

To create content, visit the `/admin` page and log in with your GitHub account. From there, you can create new blog posts and pages using the intuitive editor interface.
