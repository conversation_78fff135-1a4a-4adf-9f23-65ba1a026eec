---
title: "Welcome to My Blog"
date: "2024-01-01T10:00:00.000Z"
tags: ["welcome", "first-post"]
draft: false
---

# Welcome to My Blog

This is my first blog post created with DecapCMS! This content management system allows me to:

- Write blog posts in markdown
- Manage content through a user-friendly interface
- Automatically commit changes to git
- Deploy seamlessly to Vercel

## Getting Started

DecapCMS provides a powerful editorial workflow that integrates directly with your git repository. When you create or edit content through the admin interface, it creates pull requests that you can review before merging.

## Features

- **Git-based workflow**: All content is stored in your repository
- **Editorial workflow**: Review changes before publishing
- **Media management**: Upload and manage images
- **Markdown support**: Write content in markdown format
- **Customizable**: Configure fields and collections to match your needs

Stay tuned for more posts!
